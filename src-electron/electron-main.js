import { app, BrowserWindow, dialog, ipc<PERSON><PERSON>, Menu } from 'electron'
import path from 'path'
import os from 'os'
import { getGTAVersion, quitGtAudio } from "../src/utils/macControl"
import HearWrapper from "./hearWrapper"
import GTMacOfflineAsrWrapper from "./gtmacOfflineAsrWrapper"
import TranslateWrapper from "./translateWrapper"
import RealtimeTranslateWindow from './realtimeTranslateWindow'
import GTEAudioWrapper from "./GTEAudioWrapper"
import AutoUpdaterWrapper from "./AutoUpdateWrapper"
import TextLanguageDetector from "./textLanguageDetector"
import WinLiveCaptionController from "./winLiveCaptionController"
import Store from 'electron-store'
import { getWinMic, setWinMic, getWinOutput, setWinOutput } from '@/utils/controlWinMicVolume'
import volume from '../src/utils/controlMacMicVolume'
import { Console } from 'console'
const exec = require('child_process').exec
const fs = require('fs');
const { execSync } = require('child_process');
const spawn = require('child_process').spawn
const http = require('http');
const https = require('https');
const { existsSync, mkdirSync, createWriteStream, unlink } = require('fs');
const extract = require('extract-zip');
const macAudioDevices = require('@spotxyz/macos-audio-devices');
import log from 'electron-log'

// console.log('process.arch ',process.arch) arm64
// needed in case process is undefined under Linux
const platform = process.platform || os.platform()
if (process.env.PROD) {
  global.__statics = __dirname
}
//是否需要重新安装GTEController
let needInstallGTEControll = false
let store = new Store();
let mainWindow
let hearWrapper
let translateWrapper
let realtimeTranslateWindow
let autoUpdaterWrapper
let gteAudioWrapper
let gtmacOfflineAsrWrapper
let textLanguageDetector
let winLiveCaptionController
console.log('userData ', app.getPath('userData'))
let gteControllerVersion = store.get('gteControllerVersion') || '0.0.3'
let latestVersion = '0.0.6'
if(gteControllerVersion !== latestVersion){
  needInstallGTEControll = true
}
// 获取应用安装目录
const exePath = app.getPath('exe');
// 获取应用安装目录的父目录
const parentDirectory = path.dirname(exePath);
console.log('应用安装目录的父目录:', parentDirectory);
// setTimeout(() => {
//   dialog.showMessageBox({
//     title: '应用目录信息',
//     message: `应用安装目录: ${appInstallationPath}\n应用安装目录的父目录: ${parentDirectory}`
// });
// }, 2000);
function createWindow() {
  mainWindow = new BrowserWindow({
    icon: path.resolve(__dirname, 'icons/icon.png'),
    width: 1000,
    height: 600,
    useContentSize: true,
    show: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      // More info: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/electron-preload-script
      preload: path.resolve(__dirname, process.env.QUASAR_ELECTRON_PRELOAD),
    }
  })
  hearWrapper = new HearWrapper(mainWindow)
  gtmacOfflineAsrWrapper = new GTMacOfflineAsrWrapper(mainWindow)
  translateWrapper = new TranslateWrapper(mainWindow)
  translateWrapper.start()

  realtimeTranslateWindow = new RealtimeTranslateWindow(mainWindow)
  realtimeTranslateWindow.start()

  gteAudioWrapper = new GTEAudioWrapper(mainWindow)
  gteAudioWrapper.start()

  autoUpdaterWrapper = new AutoUpdaterWrapper(mainWindow)
  autoUpdaterWrapper.start()

  textLanguageDetector = new TextLanguageDetector()
  winLiveCaptionController = new WinLiveCaptionController(mainWindow)
  winLiveCaptionController.setupIpcHandlers()
  winLiveCaptionController.initializeConnection()
  mainWindow.loadURL(process.env.APP_URL)

  if (process.env.DEBUGGING) {
    mainWindow.webContents.openDevTools()
  } else {
    // 只有在非调试模式且配置为不打开开发者工具时才阻止打开
    mainWindow.webContents.on('devtools-opened', () => {
      const shouldOpenDevTools = store.has('openDevTools') ? store.get('openDevTools') : false
      if (!shouldOpenDevTools) {
        mainWindow.webContents.closeDevTools()
      }
    })
  }
  //  else {
  //   // we're on production; no access to devtools pls
  //   mainWindow.webContents.on('devtools-opened', () => {
  //     mainWindow.webContents.closeDevTools()
  //   })
  // }
  mainWindow.setMenu(null)
  mainWindow.on('close',async (event)=>{
    event.preventDefault();
    realtimeTranslateWindow.close()
    hearWrapper.stopHearProcess();
    translateWrapper.close()
    gtmacOfflineAsrWrapper.stopGreenTerpOfflineAsr()
    textLanguageDetector.stopDetection()
    winLiveCaptionController.exit()
    stopPdf2word()
    await gteAudioWrapper.changeSystemOutputToDefault()
    mainWindow.destroy()
  })
  mainWindow.on('closed', async () => {
    quitGtAudio();
    // try {
    //   // 使用 execSync 同步执行命令
    //   const log = execSync('ps -e | grep -v grep | grep "' + aappName + '"');
    //   const is_running = log.toString().trim() !== '';

    //   if (!is_running) {
    //     quitGtAudio();
    //   }
    // } catch (error) {
    //   // 如果命令执行失败（例如没有找到进程），也视为未运行
    //   quitGtAudio();
    // }
    mainWindow = null;
  });
  let checkGTA = null;
  let checkGTEControl = null;
  mainWindow.on('show', () => {
    if (process.platform == 'darwin') {
        checkInstallGTA()
      // checkInstallGTEControl()
    }
  })
  function checkWindowsInstallGTEControl(){
    let appName = 'GTEController'
    let gtePath =  path.join(path.dirname(parentDirectory), appName);
    fs.access(gtePath, fs.constants.F_OK, (err) => {
      if (err) {
          console.log(`folder ${gtePath} not exist`);
          installWindowsGTEControl()
      } else {
          if(needInstallGTEControll){
            installWindowsGTEControl()
            return
          }
          console.log(`folder ${gtePath} exist`);
          let exeName = 'GTE Controller.exe'
          let exePath = path.join(gtePath,exeName)
          console.log('gte exe path ',exePath)
          exec(`"${exePath}"`)
      }
  });
  }

  function checkInstallGTEControl() {
    let appName = 'GTE Controller'
    if (checkGTEControl) {
      clearInterval(checkGTEControl)
    }
    let command = 'ls /Applications/ | grep -i \"GTE Controller\"';
    exec(command, async (error, stdout, stderr) => {
      if (stdout) {
        if(needInstallGTEControll){
          installGTEControl()
          return
        }
        console.log('GTE Controller installed ')
          const log = exec('ps -e | grep -v grep | grep "' + appName + '"');
          let is_running = false;
          log.stdout.on('data', () => {
            is_running = true;
          });
          log.stdout.on('end', () => {
            let order = 'open -a \"' + appName + '.app\"'
            exec(order)
          });
      } else {
        installGTEControl()
      }
    });
  }
  function checkInstallGTA() {
    let appName = 'Green Terp Audio'
    if (checkGTA) {
      clearInterval(checkGTA)
    }
    let command = 'ls /Applications/ | grep -i \"Green Terp Audio\"';
    exec(command, async (error, stdout, stderr) => {
      if (stdout) {
        console.log('Green Terp Audio installed ')
        //判断是什么版本
        let version = '1.0'
        let needUpgradeGTA = false
        let nowVersion = await getGTAVersion()
        console.log('nowVersion ', nowVersion)
        if (nowVersion.startsWith(version)) {
          needUpgradeGTA = true
        }
        console.log('needUpgradeGTA ', needUpgradeGTA)
        if (needUpgradeGTA) {
          quitGtAudio()
          installGTA2()
        } else {
          const log = exec('ps -e | grep -v grep | grep "' + appName + '"');
          let is_running = false;
          log.stdout.on('data', () => {
            is_running = true;
          });
          log.stdout.on('end', () => {
            let order = 'open -a \"' + appName + '.app\"'
            exec(order)
          });
        }
      } else {
        installGTA2()
      }
    });
  }
  function installWindowsGTEControl(){
    // GTEControllerSetup-0.0.3.exe
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/GTEControllerSetup.exe') : path.join(app.getAppPath(), '../../lib/GTEControllerSetup.exe')
    console.log('install windows gte ',pkgpPath)
    exec(pkgpPath)
    store.set('gteControllerVersion',latestVersion)
  }
  function installGTA2() {
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/GreenTerpAudio-2.0.pkg') : path.join(app.getAppPath(), '../../lib/GreenTerpAudio-2.0.pkg')
    exec('open ' + pkgpPath)
    console.log('Green Terp Audio not installed')
  }
  function installGTEControl(){
    let pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/GTEController.dmg') : path.join(app.getAppPath(), '../../lib/GTEController.dmg')
    exec('open ' + pkgpPath)
    console.log('GTE not installed')
    store.set('gteControllerVersion',latestVersion)
  }
  mainWindow.on('ready-to-show', function () {
    mainWindow.show() // 初始化后再显示
    if (process.platform == 'darwin') {
      // checkInstallGTA()
      checkInstallGTEControl()
    }else{
      checkWindowsInstallGTEControl()
    }
  })
}
function filterAudioDeviceName(deviceName) {
  // 去掉 "Default - " 前缀
  if (deviceName.startsWith('Default - ')) {
    deviceName = deviceName.slice(10);
  }
  const dashIndex = deviceName.indexOf(' - ');
  if (dashIndex !== -1) {
    // 如果找到 " - "，只保留其后面的部分
    deviceName = deviceName.substring(dashIndex + 3);
  }
  if (process.platform == 'darwin') {
    // 去掉最后的括号及其内容
    const lastParenIndex = deviceName.lastIndexOf(' (');
    if (lastParenIndex !== -1 && deviceName.endsWith(')')) {
      deviceName = deviceName.slice(0, lastParenIndex);
    }
  } else {
  }


  return deviceName.trim(); // 去除可能残留的首尾空格
}

ipcMain.handle('delete-asr-language', async (event, arg) => {

  console.log(arg)
  const filePath = path.join(app.getPath('userData'), 'AsrLanguagePacks', arg);
  require('fs').rmSync(filePath, { recursive: true, force: true });
  return new Promise((resolve) => {
    resolve();
  });
})
// 处理来自渲染进程的 invoke 请求
ipcMain.handle('check-asr-language', async (event, arg) => {
  let modelList = JSON.parse(arg)
  return new Promise((resolve) => {
    modelList.forEach(item => {
      const filePath = path.join(app.getPath('userData'), 'AsrLanguagePacks', item.code);
      if (existsSync(filePath)) {
        item.downloaded = true
        item.downloading = false
      } else {
        item.downloaded = false
        item.downloading = false
      }
    })
    resolve(modelList)
  });
});
ipcMain.on('checkForUpdates', async (event, info) => {
  console.log('check for updates')
  autoUpdaterWrapper.setFlag = true
  autoUpdaterWrapper.updateHandle()
})
ipcMain.on('download-file', async (event, { url, filename, id }) => {
  try {
    // 构建文件保存路径，将文件保存到 AsrLanguagePacks 文件夹下
    const filePath = path.join(app.getPath('userData'), 'AsrLanguagePacks', filename);
    const folderPath = path.dirname(filePath);
    if (!existsSync(folderPath)) {
      try {
        mkdirSync(folderPath, { recursive: true });
        console.log('Directory created successfully:', folderPath);
      } catch (mkdirError) {
        console.error('Failed to create directory:', mkdirError);
        event.sender.send('download-error', `Failed to create directory: ${mkdirError.message}`);
        return;
      }
    }
    const protocol = url.startsWith('https') ? https : http;
    const req = protocol.get(url, (res) => {
      const totalSize = parseInt(res.headers['content-length'], 10);
      let downloadedSize = 0;
      const fileStream = createWriteStream(filePath);
      res.pipe(fileStream);
      res.on('data', (chunk) => {
        downloadedSize += chunk.length;
        const progress = Math.round((downloadedSize / totalSize) * 100);
        // console.log('progress ', progress);
        event.sender.send('download-progress', { id, progress });
      });
      res.on('end', async () => {
        console.log('download success');
        try {
          await extract(filePath, { dir: folderPath });
          console.log('Unzip success');
          unlink(filePath, (err) => { });
          event.sender.send('download-success', { id });
        } catch (unzipError) {
          console.error('Failed to unzip file:', unzipError);
          event.sender.send('download-error', `Failed to unzip file: ${unzipError.message}`);
        }
      });
      res.on('error', (err) => {
        console.log('download error');
        event.sender.send('download-error', err.message);
      });
    });
    req.on('error', (err) => {
      event.sender.send('download-error', err.message);
    });
  } catch (error) {
    event.sender.send('download-error', error.message);
  }
});
//获取系统输出设备音量（system audio 模式下获取真实的设备）
ipcMain.handle('getSystemSpeakerVolume',async (event, info) => {
  return gteAudioWrapper.getSystemSpeakerVolume()
})
let loudspeakerVolume = null
ipcMain.on('changeLoudSpeakerVolume', (event, req) => {
  // { volume: 29 }
  console.log(req)
  loudspeakerVolume = req.volume
  gteAudioWrapper.changeLoudSpeakerValue(req.volume)
})

//设置音频输入设备音量
ipcMain.on('setInputDeviceVolume', (event, req) => {
  let inputAudio = process.env.PROD ? path.join(__statics, '../../lib/osx-audio-input') : path.join(app.getAppPath(), '../../lib/osx-audio-input');
  let inputAudioArgs = ['set',req.deviceName, req.volume,'input'];
  spawn(inputAudio, inputAudioArgs);
})

ipcMain.handle('startSystemAudioModeChange', async (event, req) => {
  return await systemAudioModeChange(true)
})

ipcMain.on('stopSystemAudioModeChange',async (event, req) => {
  await stopAsrFun()
})


//使用sox进行声音处理，GTE-用户设备
async function systemAudioModeChange(restartSox = true,resetDefaultOutputDevice = true){
  /**
   * 1. 保存用户当前输出设备信息
   * 2. 切换输出到GTEAudio
   * 3. 使用sox 将GTEAudio音频转到GTA
   * 4. hear进程使用GTE作为输入
   * 5. 设置系统输出音量为80%
   */
  let defaultOutputDevice
  let virtualDriverName = 'GTEAudio 2ch'
  if (process.platform == 'darwin') {
    defaultOutputDevice = macAudioDevices.getDefaultOutputDevice.sync().name
    if(resetDefaultOutputDevice && !defaultOutputDevice.includes('GTE')){
      store.set('interpretease-audio-output',defaultOutputDevice)
    }
  }else{
    virtualDriverName = await gteAudioWrapper.getWinInputGTEAudioName()
    let output = await gteAudioWrapper.getWinSystemOutputDevice()
    output.forEach(item=>{
      if(item.isDefaultMultimedia){
        defaultOutputDevice = item.name + ' (' + item.deviceName + ')'
        if(resetDefaultOutputDevice && !defaultOutputDevice.includes('GTE')){
          store.set('interpretease-audio-output',defaultOutputDevice)
        }
      }
    })
  }
  console.log('defaultOutputDevice ',defaultOutputDevice)
  if(defaultOutputDevice.includes('GTEAudio')){
    return 403
  }
  console.log('restartSox ',restartSox)
  if (defaultOutputDevice) {
    if(restartSox){
      gteAudioWrapper.changeOutputDevice2GreenTerp()
      gteAudioWrapper.changeSystemOutputFromGTE(virtualDriverName, defaultOutputDevice,loudspeakerVolume)
    }
    return defaultOutputDevice
  }else{
    return null
  }
}
//开始语音识别
ipcMain.on('startAsrUseGTEAudio', async (event, { info, restartSox,deviceList }) => {
  let virtualDriverName = 'GTEAudio 2ch'
  if (hearWrapper.hearProcess) {
    hearWrapper.stopHearProcess()
  }
  deviceList = JSON.parse(deviceList)
  if (process.platform != 'darwin') {
    virtualDriverName = await gteAudioWrapper.getWinInputGTEAudioName()
  }
  info.device = virtualDriverName
  let defaultOutputDevice = await systemAudioModeChange(restartSox)
  console.log('defaultOutputDevice ',defaultOutputDevice)
  if(defaultOutputDevice){
    if (info.asrType == 0) {
      hearWrapper.macModeReq = info
      hearWrapper.startOnlineHearProcess(info)
    } else if(info.asrType == 1){
      //{ device: 'MacBook Pro麦克风', language: 'zh-CN', useOnlineAsr: false }
      gtmacOfflineAsrWrapper.startGreenTerpOfflineAsr(info)
    }
  }
});
//设置透明度
ipcMain.on('changeWindowOpacity',(event,req)=>{
  mainWindow.setOpacity(req)
})

//开始语音识别
ipcMain.on('startAsr', async (event, req) => {
  console.log('startAsr ', req)
  if (req.asrType == 0) {
    //仅支持macos online
    if (hearWrapper.hearProcess) {
      hearWrapper.stopHearProcess()
    }
    hearWrapper.macModeReq = req
    console.log('macModeReq ', hearWrapper.macModeReq)
    hearWrapper.startOnlineHearProcess(req)
  } else if(req.asrType == 1){
    //offline 
    if (gtmacOfflineAsrWrapper.gtasrProcess) {
      gtmacOfflineAsrWrapper.stopGreenTerpOfflineAsr()
    }
    gtmacOfflineAsrWrapper.startGreenTerpOfflineAsr(req)
  }


});
//asr 状态检测，停止时停止一些进程
ipcMain.handle('asrStatus', async (event, info) => {
  console.log('asrStatus ',info.asrStatus)
  if(!info.asrStatus){
    //asr 停止
    hearWrapper.stopTime()
    hearWrapper.stopHearProcess()
    if(info.stopSox){
      gteAudioWrapper.stopSoxProcess()
    }
    gtmacOfflineAsrWrapper.stopGreenTerpOfflineAsr()
  }
})
//停止语音识别
ipcMain.handle('stopAsr', async (event, info) => {
  console.log('stopAsr ', info)
  realtimeTranslateWindow.stopAsr()
  await stopAsrFun(info)
  return { status: 'success', message: 'stopAsr处理完成' };
});
async function stopAsrFun(info){
  hearWrapper.stopTime()
  hearWrapper.stopHearProcess()
  gtmacOfflineAsrWrapper.stopGreenTerpOfflineAsr()
  if(info.stopSox){
    await gteAudioWrapper.changeSystemOutputToDefault()
  }
}

//保持在最前面
ipcMain.on("staytopClick", (event, value) => {
  mainWindow.setAlwaysOnTop(value);
})
//钉住程序
ipcMain.on("pinClick", (event, value) => {
  mainWindow.setMovable(!value)
})
//清除
ipcMain.on('clearPassword', (event, info) => {
  store.delete('password')
})
//保存密码值
ipcMain.on('savePassword', (event, info) => {
  store.set('password', info)
})
//取密码值
ipcMain.handle('getPassword', (event, value) => {
  if (store.has('password')) {
    return store.get('password')
  }
  return null
})
//保存双语阅读器的lang信息
ipcMain.on('saveBilingualReaderLangInfo', (event, info) => {
  store.set('bilingualReaderLangInfo', info)
})
//获取双语阅读器的lang信息
ipcMain.handle('getBilingualReaderLangInfo', (event, value) => {
  if (store.has('bilingualReaderLangInfo')) {
    return store.get('bilingualReaderLangInfo')
  }
  return null
})
ipcMain.on('saveUserEmail', (event, email) => {
  store.set('userEmail', email)
})

ipcMain.on('getUserEmail', (event, msg) => {
  event.returnValue = store.get('userEmail')
})

// 文本语言检测相关 IPC 处理
ipcMain.handle('detectTextLanguage', async (event, text, langs = null) => {
  try {
    console.log('收到语言检测请求:', text, '指定语言:', langs)
    const result = await textLanguageDetector.detectTextLanguage(text, langs)
    console.log('语言检测结果:', result)
    return { success: true, data: result }
  } catch (error) {
    console.error('语言检测失败:', error)
    return { success: false, error: error.message }
  }
})

// 简单语言检测（兼容旧版本）
ipcMain.handle('detectTextLanguageSimple', async (event, text) => {
  try {
    console.log('收到简单语言检测请求:', text)
    const result = await textLanguageDetector.detectTextLanguageSimple(text)
    console.log('简单语言检测结果:', result)
    return { success: true, data: result }
  } catch (error) {
    console.error('简单语言检测失败:', error)
    return { success: false, error: error.message }
  }
})

// 多语言比例检测
ipcMain.handle('detectTextLanguageWithLangs', async (event, text, langs) => {
  try {
    console.log('收到多语言检测请求:', text, '语言列表:', langs)
    const result = await textLanguageDetector.detectTextLanguageWithLangs(text, langs)
    console.log('多语言检测结果:', result)
    return { success: true, data: result }
  } catch (error) {
    console.error('多语言检测失败:', error)
    return { success: false, error: error.message }
  }
})

// 获取语言检测器支持信息
ipcMain.handle('getLanguageDetectorSupportInfo', async (event) => {
  try {
    const supportInfo = textLanguageDetector.getSupportInfo()
    console.log('语言检测器支持信息:', supportInfo)
    return { success: true, data: supportInfo }
  } catch (error) {
    console.error('获取语言检测器支持信息失败:', error)
    return { success: false, error: error.message }
  }
})


var index = 0
let pdf2wordProcess
function stopPdf2word() {
  if (pdf2wordProcess) {
    console.log('Stopping pdf2word process');
    pdf2wordProcess.kill('SIGTERM');
    pdf2wordProcess = null;
  } else {
    console.log('No pdf2word process to stop');
  }
}
function startPdf2word() {
  if (pdf2wordProcess) {
    pdf2wordProcess.kill('SIGTERM');
    pdf2wordProcess = null;
  }
  let pkgpPath = null
  if (process.platform == 'darwin') {
    pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/pdf2word/pdf2word') : path.join(app.getAppPath(), '../../lib/pdf2word/pdf2word');
  } else {
    pkgpPath = process.env.PROD ? path.join(__statics, '../../lib/pdf2word/pdf2word.exe') : path.join(app.getAppPath(), '../../lib/pdf2word/pdf2word.exe');
  }
  pdf2wordProcess = spawn(pkgpPath);
  pdf2wordProcess.stdout.on('data', (data) => {
    const output = data.toString();

    console.log('stdout:', output);
  });

  pdf2wordProcess.stderr.on('data', (data) => {
    const output = data.toString();
    console.error('stderr:', output);
  });

  pdf2wordProcess.on('close', (code) => {
    console.log(`gttranslate 子进程退出，退出码 ${code}`);
    index++
    if (index < 50) {
      startPdf2word()
    }
  });
}

app.whenReady().then(() => {
  createWindow()
  startPdf2word()
  autoUpdaterWrapper.updateHandle()
  //检查是否要打开控制台
  if (store.has('openDevTools')){
    if(store.get('openDevTools')){
      if(mainWindow && mainWindow.webContents)
      mainWindow.webContents.openDevTools()
    }
  } else{
    store.set('openDevTools',false)
  }
  // reset menu
  const template = [
    {
      label: 'Edit',
      submenu: [{ role: 'undo' }, { role: 'redo' }, { type: 'separator' }, { role: 'cut' }, { role: 'copy' }, { role: 'paste' }, { role: 'pasteandmatchstyle' }, { role: 'delete' }, { role: 'selectall' }]
    }
  ]
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        {
          role: 'about'
        },
        {
          label:'Check For Updates...',
          role: 'checkupdate',
          click: () => {
            autoUpdaterWrapper.setFlag = true
            autoUpdaterWrapper.updateHandle()
          }
        },
        {
          role: 'quit'
        }
      ]
    })
    const menu = Menu.buildFromTemplate(template)
    Menu.setApplicationMenu(menu)
  }
})

app.on('window-all-closed', () => {
  // if (platform !== 'darwin') { 
  app.quit()
  // }
})

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  }
})
